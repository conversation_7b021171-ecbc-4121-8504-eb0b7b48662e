// API related types and interfaces

import { type ChatMessage } from './chat';

// Base API Response
export interface ApiResponse<T = unknown> {
  data: T;
  success: boolean;
  message?: string;
  timestamp: string;
}

// Error Response
export interface ApiError {
  code: string;
  message: string;
  details?: unknown;
  timestamp: string;
}

// Chat API Endpoints
export interface ChatApiEndpoints {
  sendMessage: string;
  getMessages: string;
  getConversations: string;
  createConversation: string;
  deleteConversation: string;
  healthCheck: string;
}

// Send Message API
export interface SendMessagePayload {
  message: string;
  accountId: string;
  customerId: string;
  threadId?: string; // Optional threadId for conversation continuity
  conversationId?: string;
  userId?: string;
  metadata?: Record<string, unknown>;
}

export interface SendMessageApiResponse {
  message: string; // The actual response message from the API
  threadId: string; // Thread ID for conversation continuity
  messageId?: string; // Optional message ID
  conversationId?: string; // Optional conversation ID
  timestamp?: string; // Optional timestamp
  metadata?: Record<string, unknown>;
}

// Get Messages API
export interface GetMessagesParams {
  conversationId: string;
  limit?: number;
  offset?: number;
  before?: string;
  after?: string;
}

export interface GetMessagesApiResponse {
  messages: ChatMessage[];
  hasMore: boolean;
  total: number;
  conversationId: string;
}

// Conversation API
export interface CreateConversationPayload {
  title?: string;
  userId?: string;
  metadata?: Record<string, unknown>;
}

export interface ConversationApiResponse {
  id: string;
  title?: string;
  createdAt: string;
  updatedAt: string;
  messageCount: number;
  metadata?: Record<string, unknown>;
}

export interface GetConversationsParams {
  userId?: string;
  limit?: number;
  offset?: number;
  sortBy?: 'createdAt' | 'updatedAt' | 'messageCount';
  sortOrder?: 'asc' | 'desc';
}

export interface GetConversationsApiResponse {
  conversations: ConversationApiResponse[];
  hasMore: boolean;
  total: number;
}

// Health Check API
export interface HealthCheckResponse {
  status: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: string;
  version: string;
  uptime: number;
  services: {
    database: 'up' | 'down';
    ai: 'up' | 'down';
    cache: 'up' | 'down';
  };
}


export interface WebSocketTypingMessage {
  type: 'typing_start' | 'typing_stop';
  payload: {
    conversationId: string;
    userId: string;
  };
  timestamp: string;
}

// HTTP Client Configuration
export interface HttpClientConfig {
  baseURL: string;
  timeout: number;
  retries: number;
  retryDelay: number;
  headers: Record<string, string>;
}

// Request Configuration
export interface RequestConfig {
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  url: string;
  data?: unknown;
  params?: Record<string, unknown>;
  headers?: Record<string, string>;
  timeout?: number;
  retries?: number;
}

// Response Interceptor
export interface ResponseInterceptor<T = unknown> {
  onFulfilled?: (response: ApiResponse<T>) => ApiResponse<T> | Promise<ApiResponse<T>>;
  onRejected?: (error: ApiError) => unknown;
}

// Request Interceptor
export interface RequestInterceptor {
  onFulfilled?: (config: RequestConfig) => RequestConfig | Promise<RequestConfig>;
  onRejected?: (error: unknown) => unknown;
}

// Retry Configuration
export interface RetryConfig {
  retries: number;
  retryDelay: number;
  retryCondition?: (error: ApiError) => boolean;
  onRetry?: (retryCount: number, error: ApiError) => void;
}

// Cache Configuration
export interface CacheConfig {
  enabled: boolean;
  ttl: number; // Time to live in milliseconds
  maxSize: number;
  keyGenerator?: (config: RequestConfig) => string;
}

// API Client Interface
export interface ApiClient {
  get<T = unknown>(url: string, config?: Partial<RequestConfig>): Promise<ApiResponse<T>>;
  post<T = unknown>(url: string, data?: unknown, config?: Partial<RequestConfig>): Promise<ApiResponse<T>>;
  put<T = unknown>(url: string, data?: unknown, config?: Partial<RequestConfig>): Promise<ApiResponse<T>>;
  delete<T = unknown>(url: string, config?: Partial<RequestConfig>): Promise<ApiResponse<T>>;
  patch<T = unknown>(url: string, data?: unknown, config?: Partial<RequestConfig>): Promise<ApiResponse<T>>;
}

// Chat Service Interface - Simplified for basic messaging
export interface ChatService {
  sendMessage(payload: SendMessagePayload): Promise<SendMessageApiResponse>;
}


// Query Keys for TanStack Query
export const QUERY_KEYS = {
  MESSAGES: (conversationId: string) => ['messages', conversationId] as const,
  CONVERSATIONS: (userId?: string) => ['conversations', userId] as const,
  HEALTH: () => ['health'] as const,
} as const;

// Mutation Keys for TanStack Query
export const MUTATION_KEYS = {
  SEND_MESSAGE: 'sendMessage',
  CREATE_CONVERSATION: 'createConversation',
  DELETE_CONVERSATION: 'deleteConversation',
} as const;

// Default API Configuration
export const DEFAULT_API_CONFIG: HttpClientConfig = {
  baseURL: import.meta.env.VITE_API_BASE_URL || '/api',
  timeout: 10000,
  retries: 0,
  retryDelay: 10000,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
};

// Error Codes
export const API_ERROR_CODES = {
  NETWORK_ERROR: 'NETWORK_ERROR',
  TIMEOUT: 'TIMEOUT',
  UNAUTHORIZED: 'UNAUTHORIZED',
  FORBIDDEN: 'FORBIDDEN',
  NOT_FOUND: 'NOT_FOUND',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  RATE_LIMITED: 'RATE_LIMITED',
  SERVER_ERROR: 'SERVER_ERROR',
  SERVICE_UNAVAILABLE: 'SERVICE_UNAVAILABLE',
} as const;

export type ApiErrorCode = typeof API_ERROR_CODES[keyof typeof API_ERROR_CODES];
